import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function AccountPage() {
  return(
        <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <header className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">Profil</h1>
        </header>

        {/* 👇 this wrapper keeps the actual form narrow */}
        <div className="w-full max-w-xl"> {/* 448 px on large screens */}
            <Tabs defaultValue="profile">
            {/* --- tab bar ---------------------------------------------------- */}
            <TabsList className="w-full overflow-x-auto sm:overflow-visible">
                <TabsTrigger value="profile">Profil</TabsTrigger>
                <TabsTrigger value="password">Parola</TabsTrigger>
                <TabsTrigger value="preferences">Preferinte</TabsTrigger>
                </TabsList>

                <TabsContent value="profile" className="mt-6 space-y-6">
                <div className="space-y-4 max-w-md">
                    <div className="space-y-2">
                    <Label htmlFor="name">Nume</Label>
                    <Input id="name" defaultValue="John Doe" />
                    </div>

                    <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                        id="email"
                        type="email"
                        defaultValue="<EMAIL>"
                    />
                    </div>

                    <div className="space-y-2">
                    <Label htmlFor="phone">Telefon</Label>
                    <Input id="phone" type="tel" defaultValue="+****************" />
                    </div>

                    <Button className="bg-[#0066B1] hover:bg-[#004d85]">
                    Salveaza modificarile
                    </Button>
                </div>
                </TabsContent>

                <TabsContent value="password" className="mt-6 space-y-6">
                <div className="space-y-4 max-w-md">
                    <div className="space-y-2">
                    <Label htmlFor="current-password">Parola actuala</Label>
                    <Input id="current-password" type="password" />
                    </div>

                    <div className="space-y-2">
                    <Label htmlFor="new-password">Noua parola</Label>
                    <Input id="new-password" type="password" />
                    </div>

                    <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirma noua parola</Label>
                    <Input id="confirm-password" type="password" />
                    </div>

                    <Button className="bg-[#0066B1] hover:bg-[#004d85]">
                    Schimba parola
                    </Button>
                </div>
                </TabsContent>

                <TabsContent value="preferences" className="mt-6 space-y-6">
                <div className="space-y-4 max-w-md">
                    <div className="space-y-2">
                    <Label>Notificari</Label>
                    <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="order-updates"
                            className="rounded border-gray-300"
                        />
                        <label htmlFor="order-updates">Actualizari comenzi</label>
                        </div>
                        <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="promotions"
                            className="rounded border-gray-300"
                        />
                        <label htmlFor="promotions">Promotii</label>
                        </div>
                        <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="newsletter"
                            className="rounded border-gray-300"
                        />
                        <label htmlFor="newsletter"> Newsletter</label>
                        </div>
                    </div>
                    </div>

                    <Button className="bg-[#0066B1] hover:bg-[#004d85]">
                    Salveaza preferintele
                    </Button>
                </div>
                </TabsContent>
            </Tabs>
        </div>
    </div>
  )
}